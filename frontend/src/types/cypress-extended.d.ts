// Type definitions for Cypress
/// <reference types="cypress" />

declare namespace Cypress {
  interface Chainable<Subject = any> {
    // Add custom Cypress commands here
    login(email?: string, password?: string): Chainable<void>;
    logout(): Chainable<void>;
    attachFile(filePath: string): Chainable<void>;
    getByTestId(id: string): Chainable<Element>;
    getBySel(selector: string): Chainable<Element>;
    getBySelLike(selector: string): Chainable<Element>;
    navigateAuthenticated(url: string): Chainable<Element>;
    checkAuthentication(): Chainable<Element>;
    shadcnSelect(selector: string, optionText: string): Chainable<Element>;
  }
}

// Extend Chai assertions for Cypress
declare global {
  namespace Chai {
    interface Assertion {
      // Basic matchers
      toBe(expected: unknown): Assertion;
      toBeCloseTo(expected: number, precision?: number): Assertion;
      toBeDefined(): Assertion;
      toBeFalsy(): Assertion;
      toBeGreaterThan(expected: number | bigint): Assertion;
      toBeGreaterThanOrEqual(expected: number | bigint): Assertion;
      toBeInstanceOf(expected: unknown): Assertion;
      toBeLessThan(expected: number | bigint): Assertion;
      toBeLessThanOrEqual(expected: number | bigint): Assertion;
      toBeNaN(): Assertion;
      toBeNull(): Assertion;
      toBeTruthy(): Assertion;
      toBeUndefined(): Assertion;
      toContain(expected: unknown): Assertion;
      toContainEqual(expected: unknown): Assertion;
      toEqual(expected: unknown): Assertion;
      toHaveLength(expected: number): Assertion;
      toHaveProperty(keyPath: string | Array<string>, value?: unknown): Assertion;
      toMatch(expected: string | RegExp): Assertion;
      toMatchObject(expected: object | Array<object>): Assertion;
      toStrictEqual(expected: unknown): Assertion;
      toThrow(expected?: string | Error | RegExp): Assertion;
      toThrowError(expected?: string | Error | RegExp): Assertion;

      // DOM Testing Library matchers
      toBeInTheDocument(): Assertion;
      toBeVisible(): Assertion;
      toBeDisabled(): Assertion;
      toBeEnabled(): Assertion;
      toBeEmpty(): Assertion;
      toBeInvalid(): Assertion;
      toBeRequired(): Assertion;
      toBeValid(): Assertion;
      toBeChecked(): Assertion;
      toHaveAttribute(attr: string, value?: unknown): Assertion;
      toHaveClass(className: string): Assertion;
      toHaveFocus(): Assertion;
      toHaveFormValues(expectedValues: Record<string, unknown>): Assertion;
      toHaveStyle(css: Record<string, unknown>): Assertion;
      toHaveTextContent(text: string | RegExp): Assertion;
      toHaveValue(value: unknown): Assertion;

      // Jest-specific matchers
      toHaveBeenCalled(): Assertion;
      toHaveBeenCalledTimes(expected: number): Assertion;
      toHaveBeenCalledWith(...args: unknown[]): Assertion;
      toHaveBeenLastCalledWith(...args: unknown[]): Assertion;
      toHaveBeenNthCalledWith(nthCall: number, ...args: unknown[]): Assertion;
      toHaveReturned(): Assertion;
      toHaveReturnedTimes(expected: number): Assertion;
      toHaveReturnedWith(expected: unknown): Assertion;
      toHaveLastReturnedWith(expected: unknown): Assertion;
      toHaveNthReturnedWith(nthCall: number, expected: unknown): Assertion;
    }
  }

  namespace Chai {
    interface ExpectStatic {
      // Asymmetric matchers
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      any(expectedType?: any): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      anything(): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      arrayContaining(sample: Array<any>): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      objectContaining(sample: Record<string, any>): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stringContaining(expected: string): any;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      stringMatching(expected: string | RegExp): any;
      not: ExpectStatic;
    }
  }
}

export {};
