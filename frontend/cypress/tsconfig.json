{"compilerOptions": {"target": "es2020", "lib": ["es2020", "dom"], "types": ["cypress", "node"], "strict": true, "noEmit": true, "isolatedModules": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "moduleResolution": "node", "baseUrl": "../", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "support/global.d.ts"], "exclude": ["node_modules"]}