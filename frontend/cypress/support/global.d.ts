/// <reference types="cypress" />

declare global {
  namespace Cypress {
    interface Chainable<Subject = any> {
      // Custom commands
      login(email?: string, password?: string): Chainable<void>;
      logout(): Chainable<void>;
      attachFile(filePath: string): Chainable<void>;
      navigateAuthenticated(url: string): Chainable<Element>;
      checkAuthentication(): Chainable<Element>;
      shadcnSelect(selector: string, optionText: string): Chainable<Element>;
      getByTestId(id: string): Chainable<Element>;
      getBySel(selector: string): Chainable<Element>;
      getBySelLike(selector: string): Chainable<Element>;

      // Cypress built-in commands that might be missing
      session(id: string, setup: () => void, options?: any): Chainable<void>;
      intercept(method: string, url: string, response?: any): Chainable<void>;
      intercept(url: string, response?: any): Chainable<void>;
      intercept(options: any): Chainable<void>;
    }

    interface Commands {
      add<T extends keyof Chainable>(
        name: T,
        fn: (...args: any[]) => any,
        options?: any
      ): void;
    }
  }

  // Declare the global Cypress object
  const Cypress: {
    Commands: Cypress.Commands;
    env(key?: string): any;
    Blob: any;
  };
}

export {};
