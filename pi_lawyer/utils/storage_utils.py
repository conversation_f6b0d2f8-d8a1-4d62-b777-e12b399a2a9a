"""
Google Cloud Storage utilities for document storage and retrieval.
"""

import logging
import os
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import google.cloud.storage
import google.oauth2.service_account

# Aliases for better readability
storage = google.cloud.storage
service_account = google.oauth2.service_account

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
GCS_BUCKET_NAME = os.getenv("GCS_BUCKET_NAME")
GCS_SERVICE_ACCOUNT_FILE = os.getenv("GCS_SERVICE_ACCOUNT_FILE")


class StorageClient:
    """Client for interacting with Google Cloud Storage."""

    def __init__(self) -> None:
        """Initialize the GCS client."""
        try:
            # Initialize with service account
            if GCS_SERVICE_ACCOUNT_FILE:
                credentials = service_account.Credentials.from_service_account_file(
                    GCS_SERVICE_ACCOUNT_FILE
                )
                self.client = storage.Client(credentials=credentials)
            else:
                # Default credentials
                self.client = storage.Client()

            self.bucket = self.client.get_bucket(GCS_BUCKET_NAME)
            logger.info(
                f"Successfully initialized GCS client for bucket {GCS_BUCKET_NAME}"
            )
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {str(e)}")
            raise

    def generate_gcs_path(
        self,
        tenant_id: Optional[str] = None,
        case_id: Optional[str] = None,
        client_id: Optional[str] = None,
        practice_area: Optional[str] = None,
        document_category: str = "general",
        subcategory: Optional[str] = None,
        is_public: bool = False,
        jurisdiction: Optional[str] = None,
    ) -> str:
        """Generate appropriate GCS path based on document type and context."""
        date_prefix = datetime.utcnow().strftime("%Y/%m/%d")
        file_id = str(uuid.uuid4())

        if is_public:
            # Public reference documents
            if jurisdiction:
                return f"public/jurisdictions/{jurisdiction}/{document_category}/{subcategory or 'general'}/{file_id}"
            elif practice_area:
                return f"public/practice_areas/{practice_area}/{document_category}/{subcategory or 'general'}/{file_id}"
            return f"public/general/{document_category}/{file_id}"

        # Tenant-specific documents
        if not tenant_id:
            raise ValueError("Tenant ID is required for non-public documents")
        if case_id:
            # Case documents
            category_path = f"{document_category}/"
            if subcategory:
                category_path += f"{subcategory}/"

            practice_area_path = ""
            if practice_area and practice_area != "general":
                practice_area_path = f"{practice_area}/"

            return f"tenants/{tenant_id}/cases/{case_id}/{practice_area_path}{category_path}{date_prefix}/{file_id}"

        elif client_id:
            # Client-specific but not case-specific documents
            return f"tenants/{tenant_id}/clients/{client_id}/{document_category}/{subcategory or 'general'}/{date_prefix}/{file_id}"

        # Firm-wide documents
        return f"tenants/{tenant_id}/case_documents/{document_category}/{subcategory or 'general'}/{date_prefix}/{file_id}"

    def upload_file(
        self,
        file_content: bytes,
        file_name: str,
        tenant_id: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
        case_id: Optional[str] = None,
        client_id: Optional[str] = None,
        practice_area: Optional[str] = None,
        document_category: str = "general",
        subcategory: Optional[str] = None,
        is_public: bool = False,
        jurisdiction: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Upload a file to GCS.

        Args:
            file_content: The binary content of the file
            file_name: Original file name
            tenant_id: The tenant ID for isolation (required for non-public documents)
            metadata: Optional metadata for the file
            case_id: Optional case ID for case-specific documents
            client_id: Optional client ID for client-specific documents
            practice_area: Optional practice area (e.g., personal_injury, criminal_defense)
            document_category: Document category (default: "general")
            subcategory: Optional subcategory for more specific categorization
            is_public: Whether this is a public document
            jurisdiction: Optional jurisdiction for public legal documents

        Returns:
            Dict with file information including GCS path
        """
        try:
            # Generate GCS path based on provided parameters
            base_path = self.generate_gcs_path(
                tenant_id=tenant_id,
                case_id=case_id,
                client_id=client_id,
                practice_area=practice_area,
                document_category=document_category,
                subcategory=subcategory,
                is_public=is_public,
                jurisdiction=jurisdiction,
            )

            # Sanitize the filename for appending to the path
            sanitized_name = "".join(
                c if c.isalnum() or c in "._- " else "_" for c in file_name
            )
            gcs_path = f"{base_path}_{sanitized_name}"

            # Create a blob and upload
            blob = self.bucket.blob(gcs_path)

            # Set content type based on file extension
            content_type = self._get_content_type(file_name)
            if content_type:
                blob.content_type = content_type

            # Set metadata if provided
            if metadata:
                blob.metadata = metadata

            # Upload the file
            blob.upload_from_string(file_content)

            logger.info(f"Successfully uploaded file to {gcs_path}")

            return {
                "gcs_path": gcs_path,
                "file_name": file_name,
                "content_type": blob.content_type,
                "size": len(file_content),
                "created_at": datetime.now().isoformat(),
                "metadata": metadata or {},
            }
        except Exception as e:
            logger.error(f"Failed to upload file {file_name}: {str(e)}")
            raise

    def get_signed_url(self, gcs_path: str, expiration_minutes: int = 15) -> str:
        """
        Generate a signed URL for temporary access to a file.

        Args:
            gcs_path: The GCS path to the file
            expiration_minutes: URL expiration time in minutes

        Returns:
            Signed URL string
        """
        try:
            blob = self.bucket.blob(gcs_path)
            url = blob.generate_signed_url(
                version="v4",
                expiration=timedelta(minutes=expiration_minutes),
                method="GET",
            )
            return str(url)
        except Exception as e:
            logger.error(f"Failed to generate signed URL for {gcs_path}: {str(e)}")
            raise

    def delete_file(self, gcs_path: str) -> bool:
        """
        Delete a file from GCS.

        Args:
            gcs_path: The GCS path to delete

        Returns:
            True if deletion was successful
        """
        try:
            blob = self.bucket.blob(gcs_path)
            blob.delete()
            logger.info(f"Successfully deleted file at {gcs_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete file at {gcs_path}: {str(e)}")
            raise

    def list_files(
        self,
        tenant_id: Optional[str] = None,
        case_id: Optional[str] = None,
        client_id: Optional[str] = None,
        practice_area: Optional[str] = None,
        document_category: Optional[str] = None,
        subcategory: Optional[str] = None,
        is_public: bool = False,
        jurisdiction: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        List files according to the structured storage system.

        Args:
            tenant_id: The tenant ID
            case_id: Optional case ID to filter by
            client_id: Optional client ID to filter by
            practice_area: Optional practice area to filter by
            document_category: Optional document category to filter by
            subcategory: Optional subcategory to filter by
            is_public: Whether to list public documents
            jurisdiction: Optional jurisdiction for public documents

        Returns:
            List of file information dictionaries
        """
        try:
            # Determine the base prefix based on the parameters
            if is_public:
                base_prefix = "public/"
                if jurisdiction:
                    base_prefix += f"jurisdictions/{jurisdiction}/"
                    if document_category:
                        base_prefix += f"{document_category}/"
                        if subcategory:
                            base_prefix += f"{subcategory}/"
                elif practice_area:
                    base_prefix += f"practice_areas/{practice_area}/"
                    if document_category:
                        base_prefix += f"{document_category}/"
                        if subcategory:
                            base_prefix += f"{subcategory}/"
                else:
                    base_prefix += "general/"
                    if document_category:
                        base_prefix += f"{document_category}/"
            elif tenant_id:
                base_prefix = f"tenants/{tenant_id}/"
                if case_id:
                    base_prefix += f"cases/{case_id}/"
                    if practice_area:
                        base_prefix += f"{practice_area}/"
                    if document_category:
                        base_prefix += f"{document_category}/"
                        if subcategory:
                            base_prefix += f"{subcategory}/"
                elif client_id:
                    base_prefix += f"clients/{client_id}/"
                    if document_category:
                        base_prefix += f"{document_category}/"
                        if subcategory:
                            base_prefix += f"{subcategory}/"
                else:
                    base_prefix += "case_documents/"
                    if document_category:
                        base_prefix += f"{document_category}/"
                        if subcategory:
                            base_prefix += f"{subcategory}/"
            else:
                base_prefix = ""  # List everything (use with caution)

            blobs = self.client.list_blobs(self.bucket, prefix=base_prefix)

            result = []
            for blob in blobs:
                # Skip directories (they end with /)
                if blob.name.endswith("/"):
                    continue

                result.append(
                    {
                        "gcs_path": blob.name,
                        "file_name": blob.name.split("/")[-1],
                        "content_type": blob.content_type,
                        "size": blob.size,
                        "updated_at": (
                            blob.updated.isoformat() if blob.updated else None
                        ),
                        "metadata": blob.metadata or {},
                    }
                )

            return result
        except Exception as e:
            error_msg = "Failed to list files"
            if tenant_id:
                error_msg += f" for tenant {tenant_id}"
            if case_id:
                error_msg += f", case {case_id}"
            if client_id:
                error_msg += f", client {client_id}"
            logger.error(f"{error_msg}: {str(e)}")
            raise

    def _get_content_type(self, file_name: str) -> Optional[str]:
        """Determine content type based on file extension."""
        extension = file_name.lower().split(".")[-1] if "." in file_name else ""
        content_types = {
            "pdf": "application/pdf",
            "doc": "application/msword",
            "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "txt": "text/plain",
            "jpg": "image/jpeg",
            "jpeg": "image/jpeg",
            "png": "image/png",
            "gif": "image/gif",
            "mp3": "audio/mpeg",
            "mp4": "video/mp4",
        }
        return content_types.get(extension)


# Singleton instance
storage_client = StorageClient()


def test_gcs_structure(
    tenant_id: str = "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",
    auth_user_id: str = "c35cfd89-faad-4621-bc3a-dc1c26ad891c",
    run_test: bool = False,
) -> Dict[str, Any]:
    """
    Test function to demonstrate the GCS folder structure.

    Args:
        tenant_id: The test tenant ID
        auth_user_id: The test user ID
        run_test: Set to True to actually run the test

    Returns:
        Dictionary with test results
    """
    if not run_test:
        print("Test mode only. Set run_test=True to actually upload test files.")
        return {
            "status": "skipped",
            "message": "Test mode only. No files were uploaded.",
        }

    client = storage_client
    test_results = {}

    try:
        # Create some test data
        test_content = b"This is a test file for demonstrating GCS folder structure."

        # Test 1: Firm document
        result1 = client.upload_file(
            file_content=test_content,
            file_name="firm_policy.txt",
            tenant_id=tenant_id,
            document_category="policies",
            subcategory="admin",
            metadata={"creator": auth_user_id, "test": "true"},
        )
        test_results["firm_document"] = result1

        # Test 2: Case document
        case_id = "test-case-123"
        result2 = client.upload_file(
            file_content=test_content,
            file_name="medical_report.txt",
            tenant_id=tenant_id,
            case_id=case_id,
            practice_area="personal_injury",
            document_category="medical",
            subcategory="reports",
            metadata={"creator": auth_user_id, "test": "true"},
        )
        test_results["case_document"] = result2

        # Test 3: Client document
        client_id = "test-client-456"
        result3 = client.upload_file(
            file_content=test_content,
            file_name="client_intake.txt",
            tenant_id=tenant_id,
            client_id=client_id,
            document_category="intake",
            metadata={"creator": auth_user_id, "test": "true"},
        )
        test_results["client_document"] = result3

        # Test 4: Public document
        result4 = client.upload_file(
            file_content=test_content,
            file_name="texas_statutes.txt",
            is_public=True,
            jurisdiction="texas",
            document_category="laws",
            subcategory="statutes",
            metadata={"creator": auth_user_id, "test": "true"},
        )
        test_results["public_document"] = result4

        # Test 5: Practice area public document
        result5 = client.upload_file(
            file_content=test_content,
            file_name="injury_guidelines.txt",
            is_public=True,
            practice_area="personal_injury",
            document_category="references",
            subcategory="guidelines",
            metadata={"creator": auth_user_id, "test": "true"},
        )
        test_results["practice_area_document"] = result5

        # List some files to verify
        firm_files = client.list_files(
            tenant_id=tenant_id, document_category="policies"
        )
        # Fix type error by storing as a dictionary with a 'files' key containing the list
        test_results["firm_files_list"] = {"files": firm_files}

        case_files = client.list_files(tenant_id=tenant_id, case_id=case_id)
        # Fix type error by storing as a dictionary with a 'files' key containing the list
        test_results["case_files_list"] = {"files": case_files}

        return {
            "status": "success",
            "results": test_results,
            "message": "All test files uploaded successfully. Check GCS console to see the structure.",
        }

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return {"status": "error", "message": str(e)}
