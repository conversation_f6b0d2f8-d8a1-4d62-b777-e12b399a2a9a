"""
Client API endpoints for the PI Lawyer application.

This module provides REST API endpoints for managing Client entities,
including CRUD operations with proper authentication and authorization.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query
from pydantic import BaseModel

from pi_lawyer.api.auth import get_current_user, require_role, validate_api_key
from pi_lawyer.data import ClientRepository, get_client_repository
from pi_lawyer.models import Client, ClientCreate, ClientUpdate, UserProfile
from pi_lawyer.utils.structured_logging import get_logger

# Set up the router
router = APIRouter(
    prefix="/api/clients",
    tags=["clients"],
    responses={
        404: {"description": "Client not found"},
        403: {"description": "Not authorized"},
    },
)

# Service router for internal service-to-service communication
service_router = APIRouter(
    prefix="/api/service/clients",
    tags=["clients"],
    responses={
        404: {"description": "Client not found"},
        403: {"description": "Not authorized"},
    },
)

# Set up logging
logger = get_logger(__name__)


# Query parameters model
class ClientFilterParams(BaseModel):
    """Parameters for filtering clients."""

    status: Optional[str] = None
    email: Optional[str] = None
    name: Optional[str] = None
    phone: Optional[str] = None
    limit: int = Query(default=50, ge=1, le=100)
    offset: int = Query(default=0, ge=0)


@router.get("", response_model=List[Client])
async def list_clients(
    filter_params: ClientFilterParams = Depends(),
    current_user: UserProfile = Depends(get_current_user),
    client_repo: ClientRepository = Depends(get_client_repository),
) -> List[Client]:
    """
    List clients with optional filtering parameters.
    Clients are filtered by tenant_id for tenant isolation.
    """
    logger.info(
        "List clients request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
            "filters": filter_params.dict(exclude_none=True),
        },
    )

    # Enforce tenant isolation
    clients = await client_repo.list(
        tenant_id=current_user.tenant_id,
        status=filter_params.status,
        email=filter_params.email,
        name=filter_params.name,
        phone=filter_params.phone,
        limit=filter_params.limit,
        offset=filter_params.offset,
    )

    return clients


@router.get("/{client_id}", response_model=Client)
async def get_client(
    client_id: UUID = Path(..., description="The ID of the client to retrieve"),
    current_user: UserProfile = Depends(get_current_user),
    client_repo: ClientRepository = Depends(get_client_repository),
) -> Client:
    """
    Get a single client by ID.
    Ensures the user can only access clients within their tenant.
    """
    logger.info(
        "Get client request",
        extra={
            "client_id": str(client_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    client = await client_repo.get_by_id(client_id)

    if not client:
        logger.warning("Client not found", extra={"client_id": str(client_id)})
        raise HTTPException(status_code=404, detail="Client not found")
    # Enforce tenant isolation
    if client.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized access attempt",
            extra={
                "client_id": str(client_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "client_tenant": str(client.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to access this client"
        )
    return client


@router.post("", response_model=Client, status_code=201)
async def create_client(
    client_data: ClientCreate,
    current_user: UserProfile = Depends(get_current_user),
    client_repo: ClientRepository = Depends(get_client_repository),
) -> Client:
    """
    Create a new client.
    Auto-assigns the current user's tenant_id for tenant isolation.
    """
    logger.info(
        "Create client request",
        extra={
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Force tenant isolation
    client_data.tenant_id = current_user.tenant_id
    # Record the creator
    client_data.created_by = current_user.id

    try:
        # Check if client with this email already exists in the tenant
        if client_data.email:
            existing_clients = await client_repo.list(
                tenant_id=current_user.tenant_id, email=client_data.email, limit=1
            )
            if existing_clients:
                logger.warning(
                    "Client with email already exists",
                    extra={
                        "email": client_data.email,
                        "tenant_id": str(current_user.tenant_id),
                    },
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Client with email {client_data.email} already exists",
                )
        new_client = await client_repo.create(client_data)
        logger.info(
            "Client created successfully", extra={"client_id": str(new_client.id)}
        )
        return new_client
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating client: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create client: {str(e)}"
        ) from e


@router.put("/{client_id}", response_model=Client)
async def update_client(
    client_update: ClientUpdate,
    client_id: UUID = Path(..., description="The ID of the client to update"),
    current_user: UserProfile = Depends(get_current_user),
    client_repo: ClientRepository = Depends(get_client_repository),
) -> Client:
    """
    Update an existing client.
    Ensures the user can only update clients within their tenant.
    """
    logger.info(
        "Update client request",
        extra={
            "client_id": str(client_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if client exists and belongs to user's tenant
    existing_client = await client_repo.get_by_id(client_id)

    if not existing_client:
        logger.warning(
            "Client not found for update", extra={"client_id": str(client_id)}
        )
        raise HTTPException(status_code=404, detail="Client not found")
    # Enforce tenant isolation
    if existing_client.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized update attempt",
            extra={
                "client_id": str(client_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "client_tenant": str(existing_client.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to update this client"
        )
    # Record who made the update
    client_update.updated_by = current_user.id

    try:
        # Check if updating to an email that's already used by another client
        if client_update.email and client_update.email != existing_client.email:
            existing_clients = await client_repo.list(
                tenant_id=current_user.tenant_id, email=client_update.email, limit=1
            )
            if existing_clients and existing_clients[0].id != client_id:
                logger.warning(
                    "Client with email already exists",
                    extra={
                        "email": client_update.email,
                        "tenant_id": str(current_user.tenant_id),
                    },
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Another client with email {client_update.email} already exists",
                )
        updated_client = await client_repo.update(client_id, client_update)
        logger.info("Client updated successfully", extra={"client_id": str(client_id)})
        return updated_client
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating client: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to update client: {str(e)}"
        ) from e


@router.delete("/{client_id}", status_code=204)
async def delete_client(
    client_id: UUID = Path(..., description="The ID of the client to delete"),
    current_user: UserProfile = Depends(get_current_user),
    client_repo: ClientRepository = Depends(get_client_repository),
    _: UserProfile = Depends(require_role(["partner", "attorney"])),
) -> None:
    """
    Delete a client.
    Requires partner or attorney role.
    Ensures the user can only delete clients within their tenant.
    """
    logger.info(
        "Delete client request",
        extra={
            "client_id": str(client_id),
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Check if client exists and belongs to user's tenant
    existing_client = await client_repo.get_by_id(client_id)

    if not existing_client:
        logger.warning(
            "Client not found for deletion", extra={"client_id": str(client_id)}
        )
        raise HTTPException(status_code=404, detail="Client not found")
    # Enforce tenant isolation
    if existing_client.tenant_id != current_user.tenant_id:
        logger.warning(
            "Unauthorized deletion attempt",
            extra={
                "client_id": str(client_id),
                "user_id": str(current_user.id),
                "user_tenant": str(current_user.tenant_id),
                "client_tenant": str(existing_client.tenant_id),
            },
        )
        raise HTTPException(
            status_code=403, detail="Not authorized to delete this client"
        )
    try:
        await client_repo.delete(client_id)
        logger.info("Client deleted successfully", extra={"client_id": str(client_id)})
    except Exception as e:
        logger.error(f"Error deleting client: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete client: {str(e)}"
        ) from e


# Endpoint for client search by partial match
@router.get("/search/{search_term}", response_model=List[Client])
async def search_clients(
    search_term: str = Path(
        ..., description="Term to search for in client name or email"
    ),
    current_user: UserProfile = Depends(get_current_user),
    client_repo: ClientRepository = Depends(get_client_repository),
    limit: int = Query(default=10, ge=1, le=50),
) -> List[Client]:
    """
    Search for clients by partial name or email match.
    Performs a case-insensitive search and returns matching clients.
    """
    logger.info(
        "Client search request",
        extra={
            "search_term": search_term,
            "user_id": str(current_user.id),
            "tenant_id": str(current_user.tenant_id),
        },
    )

    # Search for clients with matching name or email
    # This would be implemented in the repository with a specialized search method
    clients = await client_repo.search(
        tenant_id=current_user.tenant_id, search_term=search_term
    )

    # Apply limit after search is completed
    if limit and limit < len(clients):
        clients = clients[:limit]

    return clients


# Service endpoints for internal service-to-service communication


@service_router.get("/{client_id}", response_model=Client)
async def get_client_service(
    client_id: UUID = Path(..., description="The ID of the client to retrieve"),
    client_repo: ClientRepository = Depends(get_client_repository),
    _: bool = Depends(validate_api_key),
) -> Client:
    """
    Service endpoint to get a single client by ID.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info("Service get client request", extra={"client_id": str(client_id)})

    client = await client_repo.get_by_id(client_id)

    if not client:
        logger.warning(
            "Client not found in service request", extra={"client_id": str(client_id)}
        )
        raise HTTPException(status_code=404, detail="Client not found")
    return client


@service_router.post("", response_model=Client, status_code=201)
async def create_client_service(
    client_data: ClientCreate,
    client_repo: ClientRepository = Depends(get_client_repository),
    _: bool = Depends(validate_api_key),
) -> Client:
    """
    Service endpoint to create a new client.
    This endpoint is for internal service-to-service communication.
    It requires a valid service API key.
    """
    logger.info(
        "Service create client request",
        extra={
            "tenant_id": str(client_data.tenant_id),
        },
    )

    try:
        new_client = await client_repo.create(client_data)
        logger.info(
            "Client created successfully via service",
            extra={"client_id": str(new_client.id)},
        )
        return new_client
    except Exception as e:
        logger.error(f"Error creating client via service: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to create client: {str(e)}"
        ) from e
